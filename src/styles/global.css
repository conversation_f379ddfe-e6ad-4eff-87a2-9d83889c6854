
@import "tailwindcss";

:root {
  /* 颜色变量 */
  --color-primary: rgb(35, 168, 52);
  --color-primary-rgb: 35, 168, 52;
  --color-primary-hover: #073b13;
  --color-primary-light: rgba(35, 168, 52, 0.1);

  --color-secondary: #64748B;
  --color-secondary-hover: #475569;
  --color-secondary-light: rgba(100, 116, 139, 0.1);

  --color-accent: #10B981;
  --color-accent-light: rgba(16, 185, 129, 0.1);

  --color-text: #1F2937;
  --color-text-light: #4B5563;

  --color-background: #F9FAFB;
  --color-background-alt: #F3F4F6;

  --color-card: #FFFFFF;
  --color-border: #E5E7EB;

  /* 字体变量 */
  --font-sans: 'Inter', sans-serif;
  --font-display: 'Poppins', sans-serif;
}

/* 基础样式 */
body {
  background-color: var(--color-background);
  color: var(--color-text);
  font-family: var(--font-sans);
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  overflow-x: hidden;
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-display);
  font-weight: 600;
  color: var(--color-text);
  margin-bottom: 0.5rem;
}

h1 {
  font-size: 1.875rem;
  line-height: 1.25;
  font-weight: 700;
}

@media (min-width: 768px) {
  h1 {
    font-size: 2.25rem;
  }
}

h2 {
  font-size: 1.5rem;
  line-height: 1.375;
  font-weight: 700;
}

@media (min-width: 768px) {
  h2 {
    font-size: 1.875rem;
  }
}

h3 {
  font-size: 1.25rem;
  line-height: 1.5;
}

@media (min-width: 768px) {
  h3 {
    font-size: 1.5rem;
  }
}

p {
  margin-bottom: 1rem;
}

/* 动画 */
.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* 自定义容器 */
.container-custom {
  width: 91.666667%; /* 11/12 的宽度 */
  max-width: 1536px; /* 等同于 max-w-screen-2xl */
  margin-left: auto;
  margin-right: auto;
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}

@media (min-width: 640px) {
  .container-custom {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

/* 区块样式 */
.section-hero,
.section-content {
  padding-top: 4rem;
  padding-bottom: 4rem;
  position: relative;
}

.section-title {
  font-size: 1.875rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
}

.section-subtitle {
  font-size: 1.125rem;
  color: var(--color-text-light);
  margin-bottom: 2rem;
  max-width: 42rem;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
  font-weight: 500;
  padding: 0.625rem 1.25rem;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  text-decoration: none;
}

.btn-primary {
  background-color: var(--color-primary);
  color: white;
  border: 1px solid var(--color-primary);
}

.btn-primary:hover {
  background-color: var(--color-primary-hover);
  border-color: var(--color-primary-hover);
}

.btn-outline {
  background-color: transparent;
  color: var(--color-primary);
  border: 1px solid var(--color-border);
}

.btn-outline:hover {
  background-color: var(--color-primary-light);
  border-color: var(--color-primary);
}

.btn-accent {
  background-color: var(--color-accent);
  color: white;
  border: 1px solid var(--color-accent);
}

.btn-accent:hover {
  background-color: #0ea271; /* 稍微深一点的绿色 */
  border-color: #0ea271;
  transform: translateY(-1px);
}

.btn-lg {
  padding: 0.75rem 1.5rem;
  font-size: 1.125rem;
}

.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
}

/* 卡片样式 */
.card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  height: 100%;
  border: 1px solid var(--color-border);
  transition: transform 0.2s, box-shadow 0.2s;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.card-content {
  padding: 1rem;
}

.card-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

/* 徽章样式 */
.badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.badge-primary {
  background-color: var(--color-primary-light);
  color: var(--color-primary);
}

.badge-primary-solid {
  background-color: var(--color-primary);
  color: white;
}

.badge-secondary {
  background-color: var(--color-secondary-light);
  color: var(--color-secondary);
}

.badge-accent {
  background-color: var(--color-accent-light);
  color: var(--color-accent);
}

/* 标签样式 */
.tag {
  display: inline-flex;
  align-items: center;
  padding: 0.35rem 0.85rem;
  border-radius: 9999px;
  font-size: 0.85rem;
  font-weight: 500;
  transition: all 0.2s ease;
  margin: 0.2rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  border: 1px solid transparent;
}

.tag-primary {
  background-color: var(--color-primary-light);
  color: var(--color-primary);
  border-color: rgba(35, 168, 52, 0.2);
}

.tag-primary:hover {
  background-color: var(--color-primary);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(35, 168, 52, 0.2);
}

.tag-secondary {
  background-color: var(--color-secondary-light);
  color: var(--color-secondary);
  border-color: rgba(100, 116, 139, 0.2);
}

.tag-secondary:hover {
  background-color: var(--color-secondary);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(100, 116, 139, 0.2);
}

.tag-accent {
  background-color: var(--color-accent-light);
  color: var(--color-accent);
  border-color: rgba(16, 185, 129, 0.2);
}

.tag-accent:hover {
  background-color: var(--color-accent);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.2);
}

/* 活跃标签样式 */
.tag-active {
  background-color: var(--color-primary);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  font-weight: 600;
}

.tag-active.tag-secondary {
  background-color: var(--color-secondary);
}

.tag-active.tag-accent {
  background-color: var(--color-accent);
}

.tag-active .tag-count {
  background-color: rgba(255, 255, 255, 0.3);
  color: white;
}

.tag-count {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-left: 0.4rem;
  font-size: 0.7rem;
  background-color: rgba(255, 255, 255, 0.5);
  color: inherit;
  border-radius: 9999px;
  min-width: 1.2rem;
  height: 1.2rem;
  padding: 0 0.3rem;
}

/* 图标颜色 */
.icon-primary {
  color: var(--color-primary);
}

.dark .icon-primary {
  color: var(--color-primary);
  opacity: 0.9;
}

/* 卡片下载按钮样式 */
.download-btn {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: white;
  border-radius: 0.375rem;
  padding: 0.375rem 0.625rem;
  font-weight: 500;
  border: 1px solid var(--color-border);
  transition: all 0.2s ease;
  z-index: 10;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.download-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.download-btn-pdf {
  color: var(--color-primary);
}

.download-btn-pdf:hover {
  background-color: var(--color-primary-light);
  border-color: var(--color-primary);
}

.download-btn-png {
  color: var(--color-accent);
}

.download-btn-png:hover {
  background-color: var(--color-accent-light);
  border-color: var(--color-accent);
}

/* 返回首页按钮增强样式 */
.btn-back-home {
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  font-size: 1.05rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border-width: 2px;
  position: relative;
  overflow: hidden;
  z-index: 1;
  transition: all 0.3s ease;
}

.btn-back-home:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.btn-back-home:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-back-home svg {
  transition: transform 0.3s ease;
}

.btn-back-home:hover svg {
  transform: translateX(-4px);
}

/* 添加脉动动画效果，使按钮更加醒目 */
@keyframes pulse-border {
  0% {
    box-shadow: 0 0 0 0 rgba(var(--color-primary-rgb), 0.4);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(var(--color-primary-rgb), 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(var(--color-primary-rgb), 0);
  }
}

.btn-back-home-pulse {
  animation: pulse-border 2s infinite;
}

/* 响应式网格布局 - 根据容器宽度自动调整列数 */
.responsive-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

/* 在较小屏幕上减少最小宽度 */
@media (max-width: 640px) {
  .responsive-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
  }
}

/* 在中等屏幕上优化 */
@media (min-width: 641px) and (max-width: 1024px) {
  .responsive-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }
}

/* 在大屏幕上限制最大列数 */
@media (min-width: 1025px) {
  .responsive-grid {
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  }
}

/* 确保在超大屏幕上不会有太多列 */
@media (min-width: 1536px) {
  .responsive-grid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  }
}

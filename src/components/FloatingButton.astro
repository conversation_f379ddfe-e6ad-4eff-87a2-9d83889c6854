---
// 浮动按钮组件，提供回到顶部和搜索功能
---

<div class="floating-button-container">
  <!-- 回到顶部按钮 -->
  <button
    id="back-to-top-button"
    class="floating-button bg-gray-200 text-gray-700 shadow-lg mb-3 tooltip"
    aria-label="Back to top"
    title="Back to top"
  >
    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M5 15l7-7 7 7" />
    </svg>
    <span class="tooltiptext">Back to top</span>
  </button>

  <!-- 收藏按钮 - 添加动画效果 -->
  <button
    id="bookmark-button"
    class="floating-button bg-rose-100 text-rose-500 shadow-lg mb-3 tooltip animate-bookmark-pulse"
    aria-label="Bookmark this site"
    title="Bookmark this site"
  >
    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 bookmark-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
    </svg>
    <span class="tooltiptext">Bookmark</span>
  </button>

  <!-- 搜索按钮 -->
  <button
    id="search-button"
    class="floating-button bg-gray-200 text-gray-700 shadow-lg tooltip"
    aria-label="Search"
    title="Search"
  >
    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
    </svg>
    <span class="tooltiptext">Search</span>
  </button>

  <!-- 搜索弹出框 -->
  <div id="search-popup" class="search-popup">
    <div class="search-popup-content bg-white rounded-lg shadow-xl p-4">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-semibold text-gray-800">Quick Search</h3>
        <button id="close-search" class="text-gray-500 hover:text-gray-700">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
      <form action="/coloring-pages" method="get" class="flex items-center">
        <div class="relative w-full">
          <input
            type="search"
            name="search"
            class="bg-white border-2 border-[#f80] text-gray-900 text-sm rounded-lg focus:ring-[#f80] focus:border-[#e67700] block w-full p-2.5 shadow-sm transition-all duration-200"
            placeholder="Search for coloring pages..."
            autocomplete="off"
          />
        </div>
        <button type="submit" class="p-2.5 ml-2 text-sm font-medium text-white bg-[#f80] rounded-lg hover:bg-[#e67700] focus:ring-4 focus:outline-none focus:ring-[#f80]/30 shadow-sm transition-all duration-200 flex items-center">
          <span>Search</span>
        </button>
      </form>
    </div>
  </div>

  <!-- 收藏提示框 -->
  <div id="bookmark-popup" class="bookmark-popup">
    <div class="bookmark-popup-content bg-white rounded-lg shadow-xl p-4">
      <div class="flex items-center mb-2">
        <svg class="w-5 h-5 text-[#f80] mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
          <path d="M5 4a2 2 0 012-2h6a2 2 0 012 2v14l-5-2.5L5 18V4z"></path>
        </svg>
        <h3 class="text-base font-semibold text-gray-800">Bookmark This Site</h3>
      </div>
      <p class="text-sm text-gray-600 mb-3">Press <kbd class="px-2 py-1 bg-gray-100 border border-gray-300 rounded text-xs">Ctrl</kbd> + <kbd class="px-2 py-1 bg-gray-100 border border-gray-300 rounded text-xs">D</kbd> (or <kbd class="px-2 py-1 bg-gray-100 border border-gray-300 rounded text-xs">⌘</kbd> + <kbd class="px-2 py-1 bg-gray-100 border border-gray-300 rounded text-xs">D</kbd> on Mac) to bookmark this page.</p>
      <div class="flex justify-end">
        <button id="close-bookmark" class="text-sm text-[#f80] hover:text-[#e67700] font-medium">
          Got it
        </button>
      </div>
    </div>
  </div>
</div>

<style>
  .floating-button-container {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    z-index: 50;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
  }

  .floating-button {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.1);
    outline: none;
    transform: translateY(5rem);
    opacity: 0;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    position: relative;
  }

  .floating-button.visible {
    transform: translateY(0);
    opacity: 1;
  }

  .floating-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }

  .search-popup,
  .bookmark-popup {
    position: absolute;
    bottom: 4.5rem;
    right: 0;
    width: 20rem;
    max-width: calc(100vw - 4rem);
    visibility: hidden;
    opacity: 0;
    transform: translateY(1rem);
    transition: all 0.3s ease;
    z-index: 50;
    /* 确保弹出框不会超出视口右边缘 */
    transform-origin: top right;
  }

  /* 当屏幕较小时，调整弹出框位置避免遮挡内容 */
  @media (max-width: 1024px) {
    .search-popup,
    .bookmark-popup {
      right: -1rem; /* 向右偏移一点，避免遮挡主要内容 */
      width: 18rem;
    }
  }

  .search-popup.active,
  .bookmark-popup.active {
    visibility: visible;
    opacity: 1;
    transform: translateY(0);
  }

  /* 收藏提示框位置调整 */
  .bookmark-popup {
    bottom: 8rem;
  }

  /* 工具提示样式 */
  .tooltip {
    position: relative;
  }

  .tooltip .tooltiptext {
    visibility: hidden;
    width: auto;
    min-width: 80px;
    background-color: rgba(0, 0, 0, 0.75);
    color: white;
    text-align: center;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 12px;
    white-space: nowrap;

    /* 定位 */
    position: absolute;
    right: calc(100% + 10px);
    top: 50%;
    transform: translateY(-50%);
    z-index: 1;

    /* 淡入淡出效果 */
    opacity: 0;
    transition: opacity 0.3s;
  }

  /* 添加小三角形 */
  .tooltip .tooltiptext::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 100%;
    margin-top: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: transparent transparent transparent rgba(0, 0, 0, 0.75);
  }

  /* 显示工具提示 */
  .tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
  }

  /* 收藏按钮动画 */
  @keyframes bookmark-pulse {
    0% {
      transform: scale(1);
      box-shadow: 0 0 0 0 rgba(244, 63, 94, 0.4);
    }
    70% {
      transform: scale(1.1);
      box-shadow: 0 0 0 10px rgba(244, 63, 94, 0);
    }
    100% {
      transform: scale(1);
      box-shadow: 0 0 0 0 rgba(244, 63, 94, 0);
    }
  }

  @keyframes bookmark-wiggle {
    0% { transform: rotate(0deg); }
    25% { transform: rotate(-10deg); }
    50% { transform: rotate(0deg); }
    75% { transform: rotate(10deg); }
    100% { transform: rotate(0deg); }
  }

  @keyframes bookmark-icon-float {
    0% { transform: translateY(0); }
    50% { transform: translateY(-4px); }
    100% { transform: translateY(0); }
  }

  .animate-bookmark-pulse {
    animation: bookmark-pulse 2s infinite;
  }

  .bookmark-icon {
    animation: bookmark-icon-float 3s ease-in-out infinite;
  }

  #bookmark-button:hover .bookmark-icon {
    animation: bookmark-wiggle 0.5s ease-in-out;
  }

  /* 响应式调整 */
  @media (max-width: 640px) {
    .floating-button-container {
      bottom: 1.5rem;
      right: 1.5rem;
    }

    .floating-button {
      width: 2.5rem;
      height: 2.5rem;
    }

    .floating-button svg {
      width: 1.25rem;
      height: 1.25rem;
    }

    /* 移动端隐藏工具提示 */
    .tooltip .tooltiptext {
      display: none;
    }

    .floating-button.mb-3 {
      margin-bottom: 0.5rem;
    }

    .search-popup {
      bottom: 7rem;
      width: 16rem;
      right: -2rem; /* 移动端进一步向右偏移 */
    }

    .bookmark-popup {
      bottom: 10.5rem;
      width: 16rem;
      right: -2rem; /* 移动端进一步向右偏移 */
    }
  }
</style>

<script>
  // 获取DOM元素
  const backToTopButton = document.getElementById('back-to-top-button');
  const searchButton = document.getElementById('search-button');
  const bookmarkButton = document.getElementById('bookmark-button');
  const searchPopup = document.getElementById('search-popup');
  const bookmarkPopup = document.getElementById('bookmark-popup');
  const closeSearchButton = document.getElementById('close-search');
  const closeBookmarkButton = document.getElementById('close-bookmark');

  // 调整弹出框位置，避免遮挡内容
  function adjustPopupPosition(popup: HTMLElement | null) {
    if (!popup) return;

    // 获取弹出框的边界矩形
    const popupRect = popup.getBoundingClientRect();
    const viewportWidth = window.innerWidth;

    // 如果弹出框超出视口右边缘，调整位置
    if (popupRect.right > viewportWidth - 20) { // 留20px边距
      const overflow = popupRect.right - viewportWidth + 20;
      popup.style.transform = `translateX(-${overflow}px) translateY(0)`;
    } else {
      // 重置位置
      popup.style.transform = 'translateY(0)';
    }
  }

  // 显示/隐藏浮动按钮的逻辑
  function toggleButtonVisibility() {
    if (window.scrollY > 300) {
      backToTopButton?.classList.add('visible');
      searchButton?.classList.add('visible');
      bookmarkButton?.classList.add('visible');
    } else {
      backToTopButton?.classList.remove('visible');
      searchButton?.classList.remove('visible');
      bookmarkButton?.classList.remove('visible');
      // 如果按钮隐藏，也隐藏弹出框
      searchPopup?.classList.remove('active');
      bookmarkPopup?.classList.remove('active');
    }
  }

  // 初始检查
  toggleButtonVisibility();

  // 监听滚动事件
  window.addEventListener('scroll', toggleButtonVisibility);

  // 回到顶部按钮点击事件
  backToTopButton?.addEventListener('click', (e) => {
    e.preventDefault();
    window.scrollTo({ top: 0, behavior: 'smooth' });
  });

  // 搜索按钮点击事件 - 增加点击区域和事件委托
  let isSearchOpen = false;

  // 使用事件委托，捕获整个按钮区域的点击
  document.addEventListener('click', (e) => {
    const target = e.target as Element;

    // 检查点击的是否是搜索按钮或其子元素
    if (searchButton && (target === searchButton || searchButton.contains(target))) {
      e.preventDefault();
      e.stopPropagation(); // 阻止事件冒泡

      // 关闭收藏提示框
      bookmarkPopup?.classList.remove('active');
      isBookmarkOpen = false;

      if (isSearchOpen) {
        searchPopup?.classList.remove('active');
      } else {
        searchPopup?.classList.add('active');
        // 检查并调整弹出框位置，避免遮挡内容
        adjustPopupPosition(searchPopup);
      }

      isSearchOpen = !isSearchOpen;
    }
  }, true); // 使用捕获阶段

  // 收藏按钮点击事件
  let isBookmarkOpen = false;

  document.addEventListener('click', (e) => {
    const target = e.target as Element;

    // 检查点击的是否是收藏按钮或其子元素
    if (bookmarkButton && (target === bookmarkButton || bookmarkButton.contains(target))) {
      e.preventDefault();
      e.stopPropagation(); // 阻止事件冒泡

      // 添加点击动画效果
      bookmarkButton.classList.remove('animate-bookmark-pulse');
      bookmarkButton.classList.add('animate-click');

      // 创建星星粒子效果
      createStarParticles(bookmarkButton);

      // 动画结束后恢复脉动
      setTimeout(() => {
        bookmarkButton.classList.remove('animate-click');
        bookmarkButton.classList.add('animate-bookmark-pulse');
      }, 500);

      // 关闭搜索框
      searchPopup?.classList.remove('active');
      isSearchOpen = false;

      if (isBookmarkOpen) {
        bookmarkPopup?.classList.remove('active');
      } else {
        bookmarkPopup?.classList.add('active');

        // 现代浏览器不允许自动添加书签，只能显示提示
        // 大多数浏览器需要用户手动添加书签
        bookmarkPopup?.classList.add('active');
        // 检查并调整弹出框位置，避免遮挡内容
        adjustPopupPosition(bookmarkPopup);
      }

      isBookmarkOpen = !isBookmarkOpen;
    }
  }, true);

  // 创建星星粒子效果
  function createStarParticles(button: HTMLElement) {
    const container = document.querySelector('.floating-button-container');
    if (!container) return;

    // 创建5-8个星星粒子
    const particleCount = 5 + Math.floor(Math.random() * 4);
    const stars = ['✨', '⭐', '🌟', '💫', '🔆'];

    for (let i = 0; i < particleCount; i++) {
      const particle = document.createElement('div');
      particle.innerHTML = stars[Math.floor(Math.random() * stars.length)];
      particle.className = 'absolute text-xs star-particle';

      // 获取按钮位置
      const buttonRect = button.getBoundingClientRect();
      const containerRect = container.getBoundingClientRect();

      // 设置粒子初始位置（相对于容器）
      particle.style.top = `${buttonRect.top - containerRect.top + buttonRect.height/2}px`;
      particle.style.left = `${buttonRect.left - containerRect.left + buttonRect.width/2}px`;

      // 随机方向和距离
      const angle = Math.random() * Math.PI * 2;
      const distance = 30 + Math.random() * 50;
      const duration = 700 + Math.random() * 500;

      // 设置动画
      particle.style.animation = `star-float ${duration}ms ease-out forwards`;
      particle.style.setProperty('--angle', `${angle}rad`);
      particle.style.setProperty('--distance', `${distance}px`);

      container.appendChild(particle);

      // 动画结束后移除粒子
      setTimeout(() => {
        if (particle.parentNode) {
          particle.parentNode.removeChild(particle);
        }
      }, duration);
    }
  }

  // 关闭搜索框
  closeSearchButton?.addEventListener('click', () => {
    searchPopup?.classList.remove('active');
    isSearchOpen = false;
  });

  // 关闭收藏提示框
  closeBookmarkButton?.addEventListener('click', () => {
    bookmarkPopup?.classList.remove('active');
    isBookmarkOpen = false;
  });

  // 点击弹出框外部关闭弹出框
  document.addEventListener('click', (e) => {
    const target = e.target as Element;

    // 关闭搜索框
    if (isSearchOpen &&
        searchButton && !searchButton.contains(target) &&
        searchPopup && !searchPopup.contains(target)) {
      searchPopup.classList.remove('active');
      isSearchOpen = false;
    }

    // 关闭收藏提示框
    if (isBookmarkOpen &&
        bookmarkButton && !bookmarkButton.contains(target) &&
        bookmarkPopup && !bookmarkPopup.contains(target)) {
      bookmarkPopup.classList.remove('active');
      isBookmarkOpen = false;
    }
  }, false); // 使用冒泡阶段
</script>

---
import Layout from '@/layouts/Layout.astro';
import LazyColoringCard from '@/components/LazyColoringCard.astro';
import IntersectionObserver from '@/components/IntersectionObserver.astro';
import Breadcrumb from '@/components/Breadcrumb.astro';
import { getPopularColoringPages, getPopularColoringPagesCount } from '@/data/coloringPages';
import { PERFORMANCE_CONFIG } from '@/config/performance';
import '@/styles/infinite-scroll.css';

// 获取URL参数
const url = new URL(Astro.request.url);
const page = parseInt(url.searchParams.get('page') || '1');
const pageSize = PERFORMANCE_CONFIG.pagination.popularPages;

// 获取总数
const totalItems = await getPopularColoringPagesCount();

// 预渲染更多内容以支持懒加载显示
const preloadCount = Math.min(96, totalItems); // 最多预加载96个（24行，适合懒加载）
const popularPagesWithUrls = await getPopularColoringPages(preloadCount, 0);

// 分组：每组4个卡片（一行）
const cardsPerRow = 4;
const initialRows = 12; // 初始显示12行（48个卡片）
const initialVisibleCount = initialRows * cardsPerRow;

// 计算总页数（用于传统分页，但现在主要用懒加载）
const totalPages = Math.ceil(totalItems / pageSize);

// 生成分页信息（简化版，主要用于数据传递）
const pagination = {
  currentPage: page,
  totalPages,
  totalItems,
  hasNext: page < totalPages,
  hasPrev: page > 1,
  nextPage: page < totalPages ? page + 1 : null,
  prevPage: page > 1 ? page - 1 : null,
};

// 生成页码数组
function generatePageNumbers(currentPage: number, totalPages: number): number[] {
  const maxVisible = 5;
  if (totalPages <= maxVisible) {
    return Array.from({ length: totalPages }, (_, i) => i + 1);
  }

  if (currentPage <= 3) {
    return [1, 2, 3, 4, 5];
  }

  if (currentPage >= totalPages - 2) {
    return [totalPages - 4, totalPages - 3, totalPages - 2, totalPages - 1, totalPages];
  }

  return [currentPage - 2, currentPage - 1, currentPage, currentPage + 1, currentPage + 2];
}

const pageNumbers = generatePageNumbers(pagination.currentPage, pagination.totalPages);
---

<Layout title="Popular Coloring Pages - PrintableColoringHub" description="Browse our most popular printable coloring pages. Download free PDF and PNG formats.">
  <div class="container-custom py-8">
    <div class="mb-8">
      <Breadcrumb
        items={[
          { label: "Home", href: "/" },
          { label: "Popular Coloring Pages", isActive: true }
        ]}
      />
    </div>

    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
      <h1 class="mb-4 sm:mb-0">Popular Coloring Pages</h1>
      <div class="text-sm text-gray-600" id="page-info">
        Showing 1-{Math.min(initialVisibleCount, popularPagesWithUrls.length)} of {pagination.totalItems} pages
      </div>
    </div>

    <!-- 无限滚动容器 -->
    <div id="infinite-scroll-container" class="relative">
      <IntersectionObserver threshold={0.1} rootMargin="100px">
        <div id="coloring-pages-grid" class="responsive-grid gap-6">
          {popularPagesWithUrls.map((coloringPage, index) => {
            // 计算是否应该初始显示
            const isInitiallyVisible = index < initialVisibleCount;
            const rowIndex = Math.floor(index / cardsPerRow);

            return (
              <div
                class={`lazy-reveal-card ${isInitiallyVisible ? 'visible' : 'hidden'}`}
                data-row={rowIndex}
                data-index={index}
              >
                <LazyColoringCard
                  id={coloringPage.id}
                  slug={coloringPage.slug}
                  title={coloringPage.title}
                  assetFolder={coloringPage.assetFolder}
                  categoryInfo={coloringPage.categoryInfo}
                  tags={coloringPage.tags}
                  isFree={!coloringPage.premium}
                  priority={index < 4}
                  index={index}
                />
              </div>
            );
          })}
        </div>
      </IntersectionObserver>

      <!-- 加载指示器 -->
      <div id="loading-indicator" class="hidden justify-center items-center py-8">
        <div class="flex items-center space-x-2 text-gray-600">
          <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
          <span>Loading more pages...</span>
        </div>
      </div>

      <!-- 滚动触发器 -->
      <div
        id="scroll-trigger"
        class="h-4 opacity-0"
        data-total-cards={popularPagesWithUrls.length}
        data-initial-visible={initialVisibleCount}
        data-cards-per-row={cardsPerRow}
      ></div>
    </div>



    <!-- 分页导航 -->
    {pagination.totalPages > 1 && (
      <nav class="mt-12 flex justify-center" aria-label="Pagination">
        <div class="flex items-center space-x-2">
          <!-- 上一页按钮 -->
          {pagination.hasPrev ? (
            <a
              href={`/popular?page=${pagination.prevPage}`}
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 hover:text-gray-900 transition-colors"
            >
              Previous
            </a>
          ) : (
            <span class="px-4 py-2 text-sm font-medium text-gray-400 bg-gray-100 border border-gray-300 rounded-md cursor-not-allowed">
              Previous
            </span>
          )}

          <!-- 页码 -->
          <div class="flex items-center space-x-1">
            {pageNumbers.map((pageNum) => {
              const isActive = pageNum === pagination.currentPage;

              return isActive ? (
                <span class="px-3 py-2 text-sm font-medium text-white bg-primary border border-primary rounded-md">
                  {pageNum}
                </span>
              ) : (
                <a
                  href={`/popular?page=${pageNum}`}
                  class="px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 hover:text-gray-900 transition-colors"
                >
                  {pageNum}
                </a>
              );
            })}
          </div>

          <!-- 下一页按钮 -->
          {pagination.hasNext ? (
            <a
              href={`/popular?page=${pagination.nextPage}`}
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 hover:text-gray-900 transition-colors"
            >
              Next
            </a>
          ) : (
            <span class="px-4 py-2 text-sm font-medium text-gray-400 bg-gray-100 border border-gray-300 rounded-md cursor-not-allowed">
              Next
            </span>
          )}
        </div>
      </nav>
    )}
  </div>
</Layout>

<script>
  // 简单的懒加载显示管理器
  class LazyRevealManager {
    private visibleCount: number = 0;
    private totalCards: number = 0;
    private cardsPerRow: number = 4;
    private cards: NodeListOf<Element> | null = null;
    private isRevealing: boolean = false; // 防止重复触发

    constructor() {
      this.init();
    }

    private init() {
      // 获取DOM元素和配置
      const scrollTrigger = document.getElementById('scroll-trigger');
      this.cards = document.querySelectorAll('.lazy-reveal-card');

      if (!scrollTrigger || !this.cards) return;

      // 从数据属性获取配置
      this.totalCards = parseInt(scrollTrigger.dataset.totalCards || '0');
      this.visibleCount = parseInt(scrollTrigger.dataset.initialVisible || '48');
      this.cardsPerRow = parseInt(scrollTrigger.dataset.cardsPerRow || '4');

      // 设置滚动监听
      this.setupScrollListener();
    }

    private setupScrollListener() {
      // 使用节流的滚动监听
      let ticking = false;

      const handleScroll = () => {
        if (!ticking) {
          requestAnimationFrame(() => {
            this.checkScrollPosition();
            ticking = false;
          });
          ticking = true;
        }
      };

      window.addEventListener('scroll', handleScroll, { passive: true });
    }

    private checkScrollPosition() {
      // 检查是否还有隐藏的卡片需要显示，或者正在显示中
      if (this.visibleCount >= this.totalCards || this.isRevealing) return;

      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const windowHeight = window.innerHeight;

      // 获取当前可见区域的底部位置
      const viewportBottom = scrollTop + windowHeight;

      // 获取最后一个可见卡片的位置
      const lastVisibleCard = this.cards?.[this.visibleCount - 1] as HTMLElement;
      if (!lastVisibleCard) return;

      const lastCardRect = lastVisibleCard.getBoundingClientRect();
      const lastCardBottom = scrollTop + lastCardRect.bottom;

      // 当滚动到距离最后一个可见卡片底部还有1.5个屏幕高度时就开始加载
      const triggerDistance = windowHeight * 1.5;
      const shouldReveal = viewportBottom >= lastCardBottom - triggerDistance;



      if (shouldReveal) {
        this.revealNextRow();
      }
    }

    private revealNextRow() {
      if (!this.cards || this.visibleCount >= this.totalCards || this.isRevealing) return;

      // 设置正在显示状态，防止重复触发
      this.isRevealing = true;

      // 计算要显示的行数 - 根据滚动速度动态调整
      const rowsToReveal = this.calculateRowsToReveal();
      const cardsToReveal = Math.min(rowsToReveal * this.cardsPerRow, this.totalCards - this.visibleCount);

      // 计算卡片范围
      const nextRowStart = this.visibleCount;
      const nextRowEnd = nextRowStart + cardsToReveal;

      // 显示卡片
      for (let i = nextRowStart; i < nextRowEnd; i++) {
        const card = this.cards[i] as HTMLElement;
        if (card && card.classList.contains('hidden')) {
          // 计算行内位置和行索引
          const cardIndexInBatch = i - nextRowStart;
          const rowIndex = Math.floor(cardIndexInBatch / this.cardsPerRow);
          const cardIndexInRow = cardIndexInBatch % this.cardsPerRow;

          // 错开动画：每行延迟200ms，行内每个卡片延迟100ms
          const delay = rowIndex * 200 + cardIndexInRow * 100;

          setTimeout(() => {
            card.classList.remove('hidden');
            card.classList.add('visible', 'revealing');

            // 动画完成后清理类
            setTimeout(() => {
              card.classList.remove('revealing');
            }, 600);
          }, delay);
        }
      }

      // 更新可见数量
      this.visibleCount = nextRowEnd;
      this.updatePageInfo();

      // 计算最长动画时间，然后重置状态
      const maxDelay = (rowsToReveal - 1) * 200 + (this.cardsPerRow - 1) * 100 + 600; // 最后一个卡片的动画完成时间
      setTimeout(() => {
        this.isRevealing = false;
      }, maxDelay);
    }

    private calculateRowsToReveal(): number {
      // 根据剩余卡片数量和用户滚动位置决定一次显示多少行
      const remainingCards = this.totalCards - this.visibleCount;
      const remainingRows = Math.ceil(remainingCards / this.cardsPerRow);

      // 如果剩余行数较少，一次性显示完
      if (remainingRows <= 3) {
        return remainingRows;
      }

      // 否则一次显示2-3行，确保流畅体验
      return Math.min(3, remainingRows);
    }

    private updatePageInfo() {
      // 更新页面信息显示
      const pageInfoElement = document.getElementById('page-info');
      if (pageInfoElement) {
        pageInfoElement.textContent = `Showing 1-${this.visibleCount} of ${this.totalCards} pages`;
      }
    }

    public destroy() {
      // 清理事件监听器
      window.removeEventListener('scroll', this.checkScrollPosition.bind(this));
    }
  }



  // 初始化管理器
  let lazyRevealManager: LazyRevealManager;

  document.addEventListener('DOMContentLoaded', () => {
    lazyRevealManager = new LazyRevealManager();
  });

  // 页面卸载时清理
  window.addEventListener('beforeunload', () => {
    if (lazyRevealManager) {
      lazyRevealManager.destroy();
    }
  });
</script>

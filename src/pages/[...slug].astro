---
import Layout from '@/layouts/Layout.astro';
import ColoringCard from '@/components/ColoringCard.astro';
import Breadcrumb from '@/components/Breadcrumb.astro';
import { getColoringPagesBySubcategory, getCategoriesWithSubcategories } from '@/data/coloringPages';
import type { ColoringPage } from '@/types/coloringPage';
import type { CategoryWithSubcategories } from '@/data/coloringPages';

// 启用预渲染
export const prerender = true;

// 定义静态路径
export async function getStaticPaths() {
  const categoriesWithSubs = await getCategoriesWithSubcategories();
  const paths = [];

  // 为每个主分类和子分类创建路径
  for (const category of categoriesWithSubs) {
    // 主分类路径
    paths.push({
      params: { slug: category.slug },
      props: {
        mainCategory: category.name,
        subCategory: null,
        categoryData: category
      }
    });

    // 子分类路径
    for (const subCategory of category.subcategories) {
      paths.push({
        params: { slug: subCategory.slug },
        props: {
          mainCategory: category.name,
          subCategory: subCategory.name,
          categoryData: category
        }
      });
    }
  }

  return paths;
}

// 获取URL参数和props
const { slug } = Astro.params;
const { mainCategory, subCategory, categoryData } = Astro.props;

// 获取分类页面
const coloringPages = await getColoringPagesBySubcategory(
  mainCategory,
  subCategory
);

// 不再需要预先获取下载链接，CloudflareColoringCard组件会自动处理
const coloringPagesWithUrls = coloringPages;

// 页面标题和描述
const pageTitle = subCategory
  ? `${subCategory} - ${mainCategory} Coloring Pages`
  : `${mainCategory} Coloring Pages`;

const pageDescription = subCategory
  ? `Browse our collection of free printable ${subCategory.toLowerCase()} coloring pages in the ${mainCategory.toLowerCase()} category.`
  : `Browse our collection of free printable ${mainCategory.toLowerCase()} coloring pages.`;
---

<Layout
  title={`${pageTitle} - PrintableColoringHub`}
  description={pageDescription}
>
  <div class="container-custom py-8">
    <div class="mb-8">
      <Breadcrumb
        items={[
          { label: "Home", href: "/" },
          { label: "Categories", href: "/categories" },
          ...(categoryData ? [{ label: mainCategory, href: `/${categoryData.slug}`, ...(subCategory ? {} : { isActive: true }) }] : []),
          ...(subCategory ? [{ label: subCategory, isActive: true }] : [])
        ]}
      />
    </div>

    <div class="mb-8">
      <h1 class="mb-4 capitalize">{pageTitle}</h1>

      <p class="text-lg text-gray-700 mb-6">
        {pageDescription} Click on any image to view details and download options.
      </p>

      {/* 子分类导航（仅在主分类页面显示） */}
      {!subCategory && categoryData && categoryData.subcategories.length > 0 && (
        <div class="bg-white p-4 rounded-lg shadow-sm mb-8">
          <h2 class="text-lg font-semibold mb-3">Browse by Subcategory</h2>
          <div class="flex flex-wrap gap-2">
            {categoryData.subcategories.map((sub: any) => (
              <a
                href={`/${sub.slug}`}
                class="inline-flex items-center px-3 py-1.5 bg-gray-100 hover:bg-primary/10 rounded-full text-sm transition-colors"
              >
                <span class="capitalize">{sub.name}</span>
                <span class="ml-1.5 bg-gray-200 text-gray-700 rounded-full w-5 h-5 inline-flex items-center justify-center text-xs">{sub.count}</span>
              </a>
            ))}
          </div>
        </div>
      )}

      {coloringPagesWithUrls.length > 0 ? (
        <div class="responsive-grid gap-6">
          {coloringPagesWithUrls.map((page) => (
            <ColoringCard
              id={page.id}
              slug={page.slug}
              title={page.title}
              assetFolder={page.assetFolder}
              categoryInfo={page.categoryInfo}
              tags={page.tags}
              isFree={!page.premium}
            />
          ))}
        </div>
      ) : (
        <div class="text-center py-12">
          <p class="text-xl text-gray-600">No coloring pages found in this category.</p>
          <div class="flex flex-col sm:flex-row justify-center gap-4 mt-4">
            <a href="/" class="btn btn-primary">Back to Home</a>
            <a href="/categories" class="btn btn-accent">View All Categories</a>
          </div>
        </div>
      )}
    </div>
  </div>
</Layout>

---
import Layout from '@/layouts/Layout.astro';
import ColoringCard from '@/components/ColoringCard.astro';
import Breadcrumb from '@/components/Breadcrumb.astro';
import { getTagsWithCounts, getColoringPagesByTag } from '@/data/coloringPages';
import type { ColoringPage } from '@/types/coloringPage';

// 定义标签类型
interface Tag {
  name: string;
  count: number;
  slug: string;
}

// 启用预渲染
export const prerender = true;

// 定义静态路径
export async function getStaticPaths() {
  // 获取所有标签
  const tags = await getTagsWithCounts();

  // 只为有多个内容的标签创建页面
  const filteredTags = tags.filter(tag => tag.count > 1);

  return filteredTags.map((tag: Tag) => ({
    params: { tag: tag.slug },
    props: { tagName: tag.name, tagCount: tag.count },
  }));
}

// 从 props 中获取标签信息
const { tag } = Astro.params;
const { tagName, tagCount } = Astro.props;

// 获取标签对应的着色页面
const coloringPages = await getColoringPagesByTag(tagName);

// 不再需要预先获取下载链接，CloudflareColoringCard组件会自动处理
const coloringPagesWithUrls = coloringPages;
---

<Layout
  title={`${tagName} Coloring Pages - PrintableColoringHub`}
  description={`Browse our collection of ${tagName} coloring pages. Download free PDF and PNG formats.`}
>
  <div class="container-custom py-8">
    <div class="mb-8">
      <Breadcrumb
        items={[
          { label: "Home", href: "/" },
          { label: "Tags", href: "/tags" },
          { label: tagName, isActive: true }
        ]}
      />
    </div>

    <div class="flex items-center gap-3 mb-6">
      <h1 class="mb-0">{tagName} Coloring Pages</h1>
      <span class="tag tag-primary">
        <span>{tagName}</span>
        <span class="tag-count">{tagCount}</span>
      </span>
    </div>

    <p class="text-lg text-gray-700 mb-8">
      Browse our collection of {tagCount} free printable coloring pages tagged with "{tagName}".
      Click on any image to view details and download options.
    </p>

    {coloringPagesWithUrls.length > 0 ? (
      <div class="responsive-grid gap-6">
        {coloringPagesWithUrls.map((page) => (
          <ColoringCard
            id={page.id}
            slug={page.slug}
            title={page.title}
            assetFolder={page.assetFolder}
            categoryInfo={page.categoryInfo}
            tags={page.tags}
            isFree={!page.premium}
          />
        ))}
      </div>
    ) : (
      <div class="text-center py-12">
        <p class="text-xl text-gray-600">No coloring pages found with this tag.</p>
        <div class="flex flex-col sm:flex-row justify-center gap-4 mt-4">
          <a href="/" class="btn btn-primary">Back to Home</a>
          <a href="/all-pages" class="btn btn-accent">View All Pages</a>
        </div>
      </div>
    )}
  </div>
</Layout>
